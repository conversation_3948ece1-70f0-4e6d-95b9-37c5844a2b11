"""
المحلل المحسن متعدد الإطارات الزمنية - الملف الرئيسي
Enhanced Multi-Timeframe Analyzer - Main Integration File

هذا الملف يجمع جميع مكونات النظام المحسن:
1. التحليل الهرمي المتكامل
2. نظام تأكيد الإشارات متعدد المستويات
3. جمع البيانات المحسن
4. تحليل التناقضات والتوافقات
5. إنتاج التوصيات النهائية
"""

import logging
import asyncio
import numpy as np
from datetime import datetime
from typing import Dict, Any, Optional, List

# استيراد المكونات المطورة
from .enhanced_multi_timeframe import (
    HierarchicalAnalysis, TradingStyle, MarketCondition, 
    SignalStrength, TimeframeConfig
)
from .signal_confirmation import SignalConfirmation, TimeframeConflictAnalysis
from .enhanced_data_collector import EnhancedDataCollector

# إعداد السجل
logger = logging.getLogger(__name__)

class EnhancedMultiTimeframeAnalyzer:
    """المحلل المحسن متعدد الإطارات الزمنية"""
    
    def __init__(self, binance_manager=None, api_manager=None):
        # تهيئة المكونات
        self.data_collector = EnhancedDataCollector(binance_manager, api_manager)
        self.signal_confirmation = SignalConfirmation()
        self.conflict_analyzer = TimeframeConflictAnalysis()
        
        # إعدادات التحليل
        self.default_trading_style = TradingStyle.DAY_TRADING
        self.analysis_cache = {}
        self.cache_timeout = 300  # 5 دقائق
    
    async def analyze_symbol(self, symbol: str, trading_style: TradingStyle = None, 
                           user_id: str = None) -> Dict[str, Any]:
        """تحليل شامل لرمز العملة"""
        try:
            # استخدام نمط التداول الافتراضي إذا لم يتم تحديده
            if trading_style is None:
                trading_style = self.default_trading_style
            
            logger.info(f"بدء التحليل المحسن لـ {symbol} بنمط {trading_style.value}")
            
            # 1. جمع البيانات من إطارات زمنية متعددة
            timeframes_data = await self.data_collector.collect_multi_timeframe_data(
                symbol, trading_style, user_id
            )
            
            if not timeframes_data:
                return self._create_error_response("فشل في جمع البيانات")
            
            # 2. التحليل الهرمي المتكامل
            hierarchical_analyzer = HierarchicalAnalysis(trading_style)
            hierarchical_analysis = hierarchical_analyzer.analyze_top_down(timeframes_data)
            
            # 3. تحليل التناقضات والتوافقات
            conflict_analysis = self.conflict_analyzer.analyze_conflicts(timeframes_data)
            
            # 4. تأكيد الإشارات
            primary_signal = hierarchical_analysis.get('recommendation', {})
            signal_confirmation = await self._confirm_primary_signal(primary_signal, timeframes_data)
            
            # 5. تقييم المخاطر متعدد الأبعاد
            risk_assessment = await self._assess_multi_dimensional_risk(timeframes_data, hierarchical_analysis)
            
            # 6. إنتاج التوصية النهائية
            final_recommendation = self._generate_final_recommendation(
                hierarchical_analysis, conflict_analysis, signal_confirmation, risk_assessment
            )
            
            # 7. تجميع النتائج النهائية
            analysis_result = {
                'symbol': symbol,
                'trading_style': trading_style.value,
                'analysis_timestamp': datetime.now().isoformat(),
                'timeframes_analyzed': list(timeframes_data.keys()),
                
                # التحليلات الأساسية
                'hierarchical_analysis': hierarchical_analysis,
                'conflict_analysis': conflict_analysis,
                'signal_confirmation': signal_confirmation,
                'risk_assessment': risk_assessment,
                
                # التوصية النهائية
                'final_recommendation': final_recommendation,
                
                # معلومات إضافية
                'data_quality': self._assess_data_quality(timeframes_data),
                'analysis_confidence': final_recommendation.get('overall_confidence', 0),
                'market_condition': self._determine_market_condition(timeframes_data),
                
                # إحصائيات الأداء
                'performance_stats': {
                    'timeframes_count': len(timeframes_data),
                    'indicators_calculated': self._count_indicators(timeframes_data),
                    'cache_hit_rate': self._calculate_cache_hit_rate()
                }
            }
            
            logger.info(f"تم إكمال التحليل المحسن لـ {symbol} بنجاح")
            return analysis_result
            
        except Exception as e:
            logger.error(f"خطأ في التحليل المحسن لـ {symbol}: {str(e)}")
            return self._create_error_response(f"خطأ في التحليل: {str(e)}")
    
    async def _confirm_primary_signal(self, primary_signal: Dict[str, Any], 
                                    timeframes_data: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """تأكيد الإشارة الأساسية"""
        try:
            if not primary_signal:
                return {'confirmed': False, 'reason': 'no_primary_signal'}
            
            # تأكيد الإشارة من مصادر متعددة
            confirmation_result = self.signal_confirmation.confirm_signal(primary_signal, timeframes_data)
            
            return confirmation_result
            
        except Exception as e:
            logger.error(f"خطأ في تأكيد الإشارة الأساسية: {str(e)}")
            return {'confirmed': False, 'reason': 'confirmation_error'}
    
    async def _assess_multi_dimensional_risk(self, timeframes_data: Dict[str, Dict[str, Any]], 
                                           hierarchical_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """تقييم المخاطر متعدد الأبعاد"""
        try:
            risk_factors = {}
            
            # 1. مخاطر التقلبات
            volatility_risk = self._calculate_volatility_risk(timeframes_data)
            risk_factors['volatility'] = volatility_risk
            
            # 2. مخاطر انعكاس الاتجاه
            reversal_risk = self._assess_reversal_risk(timeframes_data, hierarchical_analysis)
            risk_factors['trend_reversal'] = reversal_risk
            
            # 3. مخاطر الدعم والمقاومة
            support_resistance_risk = self._evaluate_support_resistance_risk(timeframes_data)
            risk_factors['support_resistance'] = support_resistance_risk
            
            # 4. مخاطر الحجم
            volume_risk = self._analyze_volume_risk(timeframes_data)
            risk_factors['volume'] = volume_risk
            
            # 5. مخاطر التناقضات بين الإطارات
            timeframe_conflict_risk = self._assess_timeframe_conflict_risk(timeframes_data)
            risk_factors['timeframe_conflicts'] = timeframe_conflict_risk
            
            # حساب المخاطر الإجمالية
            overall_risk = self._calculate_overall_risk(risk_factors)
            
            return {
                'risk_factors': risk_factors,
                'overall_risk_score': overall_risk,
                'risk_level': self._categorize_risk_level(overall_risk),
                'risk_recommendations': self._generate_risk_recommendations(risk_factors)
            }
            
        except Exception as e:
            logger.error(f"خطأ في تقييم المخاطر: {str(e)}")
            return {
                'risk_factors': {},
                'overall_risk_score': 50,
                'risk_level': 'medium',
                'risk_recommendations': ['تحليل غير مكتمل - توخي الحذر']
            }
    
    def _generate_final_recommendation(self, hierarchical_analysis: Dict[str, Any], 
                                     conflict_analysis: Dict[str, Any],
                                     signal_confirmation: Dict[str, Any], 
                                     risk_assessment: Dict[str, Any]) -> Dict[str, Any]:
        """إنتاج التوصية النهائية"""
        try:
            # استخراج المعلومات الأساسية
            primary_recommendation = hierarchical_analysis.get('recommendation', {})
            signal_confirmed = signal_confirmation.get('signal_confirmed', False)
            confidence_score = signal_confirmation.get('confidence_score', 0)
            overall_risk = risk_assessment.get('overall_risk_score', 50)
            
            # تحديد قوة التوصية
            recommendation_strength = self._calculate_recommendation_strength(
                primary_recommendation, signal_confirmed, confidence_score, overall_risk
            )
            
            # تحديد نوع التوصية النهائية
            if recommendation_strength >= 80 and signal_confirmed and overall_risk < 30:
                final_action = primary_recommendation.get('action', 'hold')
                recommendation_type = f"strong_{final_action}" if final_action != 'hold' else 'strong_hold'
                confidence_level = 'very_high'
            elif recommendation_strength >= 60 and signal_confirmed and overall_risk < 50:
                final_action = primary_recommendation.get('action', 'hold')
                recommendation_type = final_action
                confidence_level = 'high'
            elif recommendation_strength >= 40 and overall_risk < 70:
                final_action = primary_recommendation.get('action', 'hold')
                recommendation_type = f"weak_{final_action}" if final_action != 'hold' else 'hold'
                confidence_level = 'medium'
            else:
                recommendation_type = 'hold'
                final_action = 'hold'
                confidence_level = 'low'
            
            # تحديد نقاط الدخول والخروج
            entry_exit_points = self._calculate_entry_exit_points(
                hierarchical_analysis, risk_assessment
            )
            
            # إنشاء التوصية النهائية
            final_recommendation = {
                'action': final_action,
                'recommendation_type': recommendation_type,
                'confidence_level': confidence_level,
                'recommendation_strength': recommendation_strength,
                'overall_confidence': min(confidence_score, 100 - overall_risk),
                
                # نقاط التداول
                'entry_points': entry_exit_points.get('entry_points', []),
                'exit_points': entry_exit_points.get('exit_points', []),
                'stop_loss': entry_exit_points.get('stop_loss'),
                'take_profit': entry_exit_points.get('take_profit'),
                
                # معلومات المخاطر
                'risk_level': risk_assessment.get('risk_level', 'medium'),
                'risk_score': overall_risk,
                'risk_warnings': risk_assessment.get('risk_recommendations', []),
                
                # معلومات التأكيد
                'signal_confirmed': signal_confirmed,
                'confirmation_sources': signal_confirmation.get('total_confirmations', 0),
                'timeframe_alignment': conflict_analysis.get('overall_consensus', 'unknown'),
                
                # توصيات إضافية
                'additional_notes': self._generate_additional_notes(
                    hierarchical_analysis, conflict_analysis, risk_assessment
                ),
                'recommended_timeframe': self._suggest_optimal_timeframe(hierarchical_analysis),
                'market_timing': self._assess_market_timing(hierarchical_analysis)
            }
            
            return final_recommendation
            
        except Exception as e:
            logger.error(f"خطأ في إنتاج التوصية النهائية: {str(e)}")
            return {
                'action': 'hold',
                'recommendation_type': 'hold',
                'confidence_level': 'very_low',
                'recommendation_strength': 0,
                'overall_confidence': 0,
                'risk_level': 'high',
                'additional_notes': ['حدث خطأ في التحليل - تجنب التداول']
            }
    
    def _create_error_response(self, error_message: str) -> Dict[str, Any]:
        """إنشاء استجابة خطأ"""
        return {
            'success': False,
            'error': error_message,
            'timestamp': datetime.now().isoformat(),
            'recommendation': {
                'action': 'hold',
                'confidence_level': 'very_low',
                'risk_level': 'high',
                'additional_notes': [error_message]
            }
        }
    
    def get_analysis_summary(self, analysis_result: Dict[str, Any]) -> str:
        """الحصول على ملخص التحليل بالعربية"""
        try:
            if not analysis_result.get('success', True):
                return f"❌ فشل التحليل: {analysis_result.get('error', 'خطأ غير معروف')}"
            
            final_rec = analysis_result.get('final_recommendation', {})
            action = final_rec.get('action', 'hold')
            confidence = final_rec.get('confidence_level', 'low')
            risk = final_rec.get('risk_level', 'medium')
            
            # ترجمة الإجراءات
            action_translations = {
                'buy': '🟢 شراء',
                'strong_buy': '🟢🟢 شراء قوي',
                'sell': '🔴 بيع',
                'strong_sell': '🔴🔴 بيع قوي',
                'hold': '🟡 انتظار'
            }
            
            # ترجمة مستويات الثقة
            confidence_translations = {
                'very_high': 'عالية جداً',
                'high': 'عالية',
                'medium': 'متوسطة',
                'low': 'منخفضة',
                'very_low': 'منخفضة جداً'
            }
            
            # ترجمة مستويات المخاطر
            risk_translations = {
                'very_low': 'منخفضة جداً',
                'low': 'منخفضة',
                'medium': 'متوسطة',
                'high': 'عالية',
                'very_high': 'عالية جداً'
            }
            
            action_text = action_translations.get(action, action)
            confidence_text = confidence_translations.get(confidence, confidence)
            risk_text = risk_translations.get(risk, risk)
            
            # ترجمة نمط التداول
            trading_style_translations = {
                'scalping': 'سكالبينغ',
                'day_trading': 'تداول يومي',
                'swing_trading': 'تداول متأرجح',
                'position': 'تداول مراكز'
            }

            # ترجمة توافق الإطارات
            alignment_translations = {
                'strong_consensus': 'توافق قوي',
                'moderate_consensus': 'توافق متوسط',
                'weak_consensus': 'توافق ضعيف',
                'conflicted': 'متناقض',
                'unknown': 'غير معروف'
            }

            trading_style_text = trading_style_translations.get(
                analysis_result.get('trading_style', 'day_trading'),
                'تداول يومي'
            )

            # تنسيق آمن للرسائل لتجنب مشاكل Markdown
            summary = f"""🚀 تحليل محسن متعدد الإطارات الزمنية

🎯 التوصية: {action_text}
📊 مستوى الثقة: {confidence_text}
⚠️ مستوى المخاطر: {risk_text}
📈 نمط التداول: {trading_style_text}
⏰ الإطارات المحللة: {len(analysis_result.get('timeframes_analyzed', []))}"""

            # إضافة معلومات إضافية بتنسيق آمن
            if final_rec.get('confirmation_sources', 0) > 0:
                summary += f"\n📊 مصادر التأكيد: {final_rec.get('confirmation_sources', 0)}"

            alignment = final_rec.get('timeframe_alignment', 'unknown')
            alignment_text = alignment_translations.get(alignment, 'غير معروف')
            if alignment != 'unknown':
                summary += f"\n🔄 توافق الإطارات: {alignment_text}"

            timing = final_rec.get('market_timing', 'غير محدد')
            if timing != 'غير محدد':
                summary += f"\n⏰ توقيت السوق: {timing}"

            # إضافة التحذيرات إذا كانت موجودة
            warnings = final_rec.get('risk_warnings', [])
            if warnings:
                summary += f"\n\n⚠️ تحذيرات مهمة:"
                for warning in warnings[:2]:  # أول تحذيرين فقط
                    summary += f"\n• {warning}"
            
            return summary.strip()
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء ملخص التحليل: {str(e)}")
            return "❌ خطأ في إنشاء ملخص التحليل"

    # دوال مساعدة لتقييم المخاطر
    def _calculate_volatility_risk(self, timeframes_data: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """حساب مخاطر التقلبات"""
        try:
            volatility_scores = []

            for timeframe, data in timeframes_data.items():
                atr = data.get('atr', 0)
                price = data.get('price', 1)

                if atr and price:
                    volatility_percentage = (atr / price) * 100
                    volatility_scores.append(volatility_percentage)

            if volatility_scores:
                avg_volatility = sum(volatility_scores) / len(volatility_scores)
                risk_score = min(avg_volatility * 10, 100)  # تطبيع النتيجة

                if risk_score > 80:
                    risk_level = 'very_high'
                elif risk_score > 60:
                    risk_level = 'high'
                elif risk_score > 40:
                    risk_level = 'medium'
                elif risk_score > 20:
                    risk_level = 'low'
                else:
                    risk_level = 'very_low'

                return {
                    'score': risk_score,
                    'level': risk_level,
                    'average_volatility': avg_volatility,
                    'description': f'متوسط التقلبات: {avg_volatility:.2f}%'
                }

            return {'score': 50, 'level': 'medium', 'description': 'بيانات غير كافية'}

        except Exception as e:
            logger.error(f"خطأ في حساب مخاطر التقلبات: {str(e)}")
            return {'score': 50, 'level': 'medium', 'description': 'خطأ في الحساب'}

    def _assess_reversal_risk(self, timeframes_data: Dict[str, Dict[str, Any]],
                            hierarchical_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """تقييم مخاطر انعكاس الاتجاه"""
        try:
            reversal_indicators = 0
            total_indicators = 0

            for timeframe, data in timeframes_data.items():
                # فحص مؤشرات الانعكاس
                rsi = data.get('rsi', 50)
                stoch_k = data.get('stoch_k', 50)

                total_indicators += 2

                # RSI في مناطق التشبع
                if rsi >= 70 or rsi <= 30:
                    reversal_indicators += 1

                # Stochastic في مناطق التشبع
                if stoch_k >= 80 or stoch_k <= 20:
                    reversal_indicators += 1

            if total_indicators > 0:
                reversal_probability = (reversal_indicators / total_indicators) * 100

                if reversal_probability > 70:
                    risk_level = 'very_high'
                elif reversal_probability > 50:
                    risk_level = 'high'
                elif reversal_probability > 30:
                    risk_level = 'medium'
                else:
                    risk_level = 'low'

                return {
                    'score': reversal_probability,
                    'level': risk_level,
                    'probability': reversal_probability,
                    'description': f'احتمالية الانعكاس: {reversal_probability:.1f}%'
                }

            return {'score': 30, 'level': 'medium', 'description': 'بيانات غير كافية'}

        except Exception as e:
            logger.error(f"خطأ في تقييم مخاطر الانعكاس: {str(e)}")
            return {'score': 30, 'level': 'medium', 'description': 'خطأ في التقييم'}

    def _evaluate_support_resistance_risk(self, timeframes_data: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """تقييم مخاطر الدعم والمقاومة"""
        try:
            risk_factors = []

            for timeframe, data in timeframes_data.items():
                price = data.get('price', 0)
                bb_upper = data.get('bb_upper', 0)
                bb_lower = data.get('bb_lower', 0)

                if all([price, bb_upper, bb_lower]):
                    # حساب موقع السعر في النطاق
                    range_size = bb_upper - bb_lower
                    if range_size > 0:
                        position = (price - bb_lower) / range_size

                        # كلما اقترب السعر من الحدود، زادت المخاطر
                        if position > 0.9 or position < 0.1:
                            risk_factors.append(80)
                        elif position > 0.8 or position < 0.2:
                            risk_factors.append(60)
                        elif position > 0.7 or position < 0.3:
                            risk_factors.append(40)
                        else:
                            risk_factors.append(20)

            if risk_factors:
                avg_risk = sum(risk_factors) / len(risk_factors)

                if avg_risk > 70:
                    risk_level = 'very_high'
                elif avg_risk > 50:
                    risk_level = 'high'
                elif avg_risk > 30:
                    risk_level = 'medium'
                else:
                    risk_level = 'low'

                return {
                    'score': avg_risk,
                    'level': risk_level,
                    'description': f'مخاطر الدعم/المقاومة: {avg_risk:.1f}%'
                }

            return {'score': 40, 'level': 'medium', 'description': 'بيانات غير كافية'}

        except Exception as e:
            logger.error(f"خطأ في تقييم مخاطر الدعم والمقاومة: {str(e)}")
            return {'score': 40, 'level': 'medium', 'description': 'خطأ في التقييم'}

    def _analyze_volume_risk(self, timeframes_data: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """تحليل مخاطر الحجم"""
        try:
            volume_ratios = []

            for timeframe, data in timeframes_data.items():
                volume_ratio = data.get('volume_ratio', 1)
                if volume_ratio:
                    volume_ratios.append(volume_ratio)

            if volume_ratios:
                avg_volume_ratio = sum(volume_ratios) / len(volume_ratios)

                # حجم منخفض = مخاطر عالية
                if avg_volume_ratio < 0.5:
                    risk_score = 80
                    risk_level = 'very_high'
                    description = 'حجم تداول منخفض جداً'
                elif avg_volume_ratio < 0.8:
                    risk_score = 60
                    risk_level = 'high'
                    description = 'حجم تداول منخفض'
                elif avg_volume_ratio > 3:
                    risk_score = 70
                    risk_level = 'high'
                    description = 'حجم تداول مرتفع بشكل غير طبيعي'
                elif avg_volume_ratio > 2:
                    risk_score = 40
                    risk_level = 'medium'
                    description = 'حجم تداول مرتفع'
                else:
                    risk_score = 20
                    risk_level = 'low'
                    description = 'حجم تداول طبيعي'

                return {
                    'score': risk_score,
                    'level': risk_level,
                    'volume_ratio': avg_volume_ratio,
                    'description': description
                }

            return {'score': 50, 'level': 'medium', 'description': 'بيانات حجم غير متاحة'}

        except Exception as e:
            logger.error(f"خطأ في تحليل مخاطر الحجم: {str(e)}")
            return {'score': 50, 'level': 'medium', 'description': 'خطأ في التحليل'}

    def _assess_timeframe_conflict_risk(self, timeframes_data: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """تقييم مخاطر التناقضات بين الإطارات"""
        try:
            conflicts = self.conflict_analyzer.analyze_conflicts(timeframes_data)

            total_comparisons = len(conflicts.get('conflicts', [])) + len(conflicts.get('confirmations', []))
            conflict_count = len(conflicts.get('conflicts', []))

            if total_comparisons > 0:
                conflict_ratio = (conflict_count / total_comparisons) * 100

                if conflict_ratio > 70:
                    risk_level = 'very_high'
                elif conflict_ratio > 50:
                    risk_level = 'high'
                elif conflict_ratio > 30:
                    risk_level = 'medium'
                else:
                    risk_level = 'low'

                return {
                    'score': conflict_ratio,
                    'level': risk_level,
                    'conflicts_count': conflict_count,
                    'total_comparisons': total_comparisons,
                    'description': f'تناقضات بين الإطارات: {conflict_ratio:.1f}%'
                }

            return {'score': 30, 'level': 'medium', 'description': 'بيانات غير كافية للمقارنة'}

        except Exception as e:
            logger.error(f"خطأ في تقييم مخاطر التناقضات: {str(e)}")
            return {'score': 30, 'level': 'medium', 'description': 'خطأ في التقييم'}

    def _calculate_overall_risk(self, risk_factors: Dict[str, Dict[str, Any]]) -> float:
        """حساب المخاطر الإجمالية"""
        try:
            # أوزان عوامل المخاطر
            weights = {
                'volatility': 0.25,
                'trend_reversal': 0.25,
                'support_resistance': 0.20,
                'volume': 0.15,
                'timeframe_conflicts': 0.15
            }

            total_risk = 0
            total_weight = 0

            for factor_name, factor_data in risk_factors.items():
                weight = weights.get(factor_name, 0.1)
                score = factor_data.get('score', 50)

                total_risk += score * weight
                total_weight += weight

            if total_weight > 0:
                return total_risk / total_weight
            else:
                return 50

        except Exception as e:
            logger.error(f"خطأ في حساب المخاطر الإجمالية: {str(e)}")
            return 50

    def _categorize_risk_level(self, risk_score: float) -> str:
        """تصنيف مستوى المخاطر"""
        if risk_score >= 80:
            return 'very_high'
        elif risk_score >= 60:
            return 'high'
        elif risk_score >= 40:
            return 'medium'
        elif risk_score >= 20:
            return 'low'
        else:
            return 'very_low'

    def _generate_risk_recommendations(self, risk_factors: Dict[str, Dict[str, Any]]) -> List[str]:
        """إنتاج توصيات المخاطر"""
        try:
            recommendations = []

            for factor_name, factor_data in risk_factors.items():
                risk_level = factor_data.get('level', 'medium')

                if factor_name == 'volatility' and risk_level in ['high', 'very_high']:
                    recommendations.append('تقلبات عالية - استخدم أحجام صفقات أصغر')

                elif factor_name == 'trend_reversal' and risk_level in ['high', 'very_high']:
                    recommendations.append('احتمالية انعكاس عالية - راقب إشارات الخروج')

                elif factor_name == 'volume' and risk_level in ['high', 'very_high']:
                    recommendations.append('مشاكل في الحجم - تأكد من السيولة قبل التداول')

                elif factor_name == 'timeframe_conflicts' and risk_level in ['high', 'very_high']:
                    recommendations.append('تناقضات بين الإطارات - انتظر إشارات أوضح')

            if not recommendations:
                recommendations.append('مستوى مخاطر مقبول - تابع خطة التداول')

            return recommendations

        except Exception as e:
            logger.error(f"خطأ في إنتاج توصيات المخاطر: {str(e)}")
            return ['خطأ في تقييم المخاطر - توخي الحذر']

    def _calculate_recommendation_strength(self, primary_recommendation: Dict[str, Any],
                                         signal_confirmed: bool, confidence_score: float,
                                         overall_risk: float) -> float:
        """حساب قوة التوصية"""
        try:
            base_strength = primary_recommendation.get('entry_quality', 0)

            # تعديل القوة بناءً على التأكيد
            if signal_confirmed:
                base_strength *= 1.2
            else:
                base_strength *= 0.8

            # تعديل القوة بناءً على مستوى الثقة
            confidence_factor = confidence_score / 100
            base_strength *= (0.5 + confidence_factor * 0.5)

            # تعديل القوة بناءً على المخاطر
            risk_factor = (100 - overall_risk) / 100
            base_strength *= risk_factor

            return min(base_strength, 100)

        except Exception as e:
            logger.error(f"خطأ في حساب قوة التوصية: {str(e)}")
            return 0

    def _calculate_entry_exit_points(self, hierarchical_analysis: Dict[str, Any],
                                   risk_assessment: Dict[str, Any]) -> Dict[str, Any]:
        """حساب نقاط الدخول والخروج"""
        try:
            # استخراج بيانات السعر الحالي
            current_price = 0
            atr = 0

            # البحث عن السعر الحالي في التحليل
            short_term = hierarchical_analysis.get('short_term_signals', {})
            if short_term:
                for signal in short_term.get('signals', []):
                    if 'price' in signal.get('details', {}):
                        current_price = signal['details']['price']
                        break

            # البحث عن ATR لحساب المسافات
            medium_term = hierarchical_analysis.get('medium_term_trend', {})
            if medium_term and 'details' in medium_term:
                for signal in medium_term.get('details', {}).get('individual_signals', []):
                    if 'atr' in signal.get('details', {}):
                        atr = signal['details']['atr']
                        break

            if not current_price:
                return {}

            # حساب نقاط الدخول والخروج بناءً على ATR
            if not atr:
                atr = current_price * 0.02  # افتراض 2% كـ ATR

            recommendation = hierarchical_analysis.get('recommendation', {})
            action = recommendation.get('action', 'hold')

            entry_exit_points = {}

            if action in ['buy', 'strong_buy']:
                # نقاط دخول للشراء
                entry_exit_points['entry_points'] = [
                    round(current_price - (atr * 0.5), 6),  # دخول عند تراجع طفيف
                    round(current_price, 6),  # دخول فوري
                    round(current_price + (atr * 0.3), 6)   # دخول عند كسر المقاومة
                ]

                # وقف الخسارة
                entry_exit_points['stop_loss'] = round(current_price - (atr * 2), 6)

                # جني الأرباح
                entry_exit_points['take_profit'] = round(current_price + (atr * 3), 6)

            elif action in ['sell', 'strong_sell']:
                # نقاط دخول للبيع
                entry_exit_points['entry_points'] = [
                    round(current_price + (atr * 0.5), 6),  # دخول عند ارتفاع طفيف
                    round(current_price, 6),  # دخول فوري
                    round(current_price - (atr * 0.3), 6)   # دخول عند كسر الدعم
                ]

                # وقف الخسارة
                entry_exit_points['stop_loss'] = round(current_price + (atr * 2), 6)

                # جني الأرباح
                entry_exit_points['take_profit'] = round(current_price - (atr * 3), 6)

            return entry_exit_points

        except Exception as e:
            logger.error(f"خطأ في حساب نقاط الدخول والخروج: {str(e)}")
            return {}

    def _generate_additional_notes(self, hierarchical_analysis: Dict[str, Any],
                                 conflict_analysis: Dict[str, Any],
                                 risk_assessment: Dict[str, Any]) -> List[str]:
        """إنتاج ملاحظات إضافية"""
        try:
            notes = []

            # ملاحظات حول التوافق
            consensus = conflict_analysis.get('overall_consensus', 'unknown')
            if consensus == 'strong_agreement':
                notes.append('توافق قوي بين جميع الإطارات الزمنية')
            elif consensus == 'partial_agreement':
                notes.append('توافق جزئي بين الإطارات الزمنية')
            elif consensus == 'conflicting':
                notes.append('تناقضات بين الإطارات الزمنية - توخي الحذر')

            # ملاحظات حول المخاطر
            overall_risk = risk_assessment.get('overall_risk_score', 50)
            if overall_risk > 70:
                notes.append('مستوى مخاطر عالي - فكر في تقليل حجم الصفقة')
            elif overall_risk < 30:
                notes.append('مستوى مخاطر منخفض - فرصة جيدة للتداول')

            # ملاحظات حول قوة الإشارة
            long_term = hierarchical_analysis.get('long_term_trend', {})
            if long_term.get('strength', 0) >= 4:
                notes.append('اتجاه طويل المدى قوي')

            return notes[:5]  # أقصى 5 ملاحظات

        except Exception as e:
            logger.error(f"خطأ في إنتاج الملاحظات الإضافية: {str(e)}")
            return ['خطأ في إنتاج الملاحظات']

    def _suggest_optimal_timeframe(self, hierarchical_analysis: Dict[str, Any]) -> str:
        """اقتراح الإطار الزمني الأمثل"""
        try:
            # تحليل قوة الإشارات في كل إطار
            long_term = hierarchical_analysis.get('long_term_trend', {})
            medium_term = hierarchical_analysis.get('medium_term_trend', {})
            short_term = hierarchical_analysis.get('short_term_signals', {})

            long_strength = long_term.get('strength', 0)
            medium_strength = medium_term.get('strength', 0)
            short_strength = short_term.get('entry_quality', 0) / 20  # تطبيع إلى 5

            if long_strength >= 4:
                return '1d أو أعلى - للاستثمار طويل المدى'
            elif medium_strength >= 4:
                return '4h إلى 1d - للتداول المتأرجح'
            elif short_strength >= 4:
                return '15m إلى 1h - للتداول اليومي'
            else:
                return '1h - إطار زمني متوازن'

        except Exception as e:
            logger.error(f"خطأ في اقتراح الإطار الزمني: {str(e)}")
            return 'غير محدد'

    def _assess_market_timing(self, hierarchical_analysis: Dict[str, Any]) -> str:
        """تقييم توقيت السوق"""
        try:
            short_term = hierarchical_analysis.get('short_term_signals', {})
            timing_score = short_term.get('timing_score', 0)

            if timing_score >= 80:
                return 'ممتاز - توقيت مثالي للدخول'
            elif timing_score >= 60:
                return 'جيد - توقيت مناسب للدخول'
            elif timing_score >= 40:
                return 'متوسط - يمكن الانتظار لتوقيت أفضل'
            elif timing_score >= 20:
                return 'ضعيف - انتظر إشارات أوضح'
            else:
                return 'سيء - تجنب التداول حالياً'

        except Exception as e:
            logger.error(f"خطأ في تقييم توقيت السوق: {str(e)}")
            return 'غير محدد'

    # دوال مساعدة إضافية
    def _assess_data_quality(self, timeframes_data: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """تقييم جودة البيانات"""
        try:
            total_indicators = 0
            valid_indicators = 0

            for data in timeframes_data.values():
                for key, value in data.items():
                    if key != 'price' and value is not None:
                        total_indicators += 1
                        if isinstance(value, (int, float)) and not np.isnan(value):
                            valid_indicators += 1

            quality_percentage = (valid_indicators / total_indicators * 100) if total_indicators > 0 else 0

            return {
                'quality_percentage': quality_percentage,
                'total_indicators': total_indicators,
                'valid_indicators': valid_indicators,
                'quality_level': 'high' if quality_percentage > 80 else 'medium' if quality_percentage > 60 else 'low'
            }

        except Exception as e:
            logger.error(f"خطأ في تقييم جودة البيانات: {str(e)}")
            return {'quality_level': 'unknown', 'quality_percentage': 0}

    def _determine_market_condition(self, timeframes_data: Dict[str, Dict[str, Any]]) -> str:
        """تحديد حالة السوق"""
        try:
            volatility_scores = []
            trend_scores = []

            for data in timeframes_data.values():
                # حساب التقلبات
                atr = data.get('atr', 0)
                price = data.get('price', 1)
                if atr and price:
                    volatility = (atr / price) * 100
                    volatility_scores.append(volatility)

                # حساب قوة الاتجاه
                adx = data.get('adx', 25)
                if adx:
                    trend_scores.append(adx)

            avg_volatility = sum(volatility_scores) / len(volatility_scores) if volatility_scores else 2
            avg_trend_strength = sum(trend_scores) / len(trend_scores) if trend_scores else 25

            if avg_volatility > 5:
                return 'volatile'
            elif avg_volatility < 1:
                return 'low_volatility'
            elif avg_trend_strength > 30:
                return 'trending'
            else:
                return 'sideways'

        except Exception as e:
            logger.error(f"خطأ في تحديد حالة السوق: {str(e)}")
            return 'unknown'

    def _count_indicators(self, timeframes_data: Dict[str, Dict[str, Any]]) -> int:
        """عد المؤشرات المحسوبة"""
        try:
            total_indicators = 0
            for data in timeframes_data.values():
                total_indicators += len([v for v in data.values() if v is not None])
            return total_indicators
        except Exception:
            return 0

    def _calculate_cache_hit_rate(self) -> float:
        """حساب معدل نجاح التخزين المؤقت"""
        try:
            cache_stats = self.data_collector.get_cache_stats()
            total = cache_stats.get('total_entries', 0)
            valid = cache_stats.get('valid_entries', 0)
            return (valid / total * 100) if total > 0 else 0
        except Exception:
            return 0
